# PlayMode.PLAY_MONEY Documentation

## Overview

`PlayMode.PLAY_MONEY` is a special play mode in the sw-management-api system that enables games to operate with virtual/social currencies instead of real money. This mode is designed to support promotional activities, demo gameplay, and social gaming experiences while maintaining the same game mechanics and business logic as real money games.

### Purpose
- Enable promotional activities and marketing campaigns
- Provide demo/trial gameplay experiences
- Support social gaming with virtual currencies
- Facilitate testing and development scenarios
- Help with Finance calculations by segregating virtual currency transactions

## PlayMode Enum Values

The system supports several play modes defined in `@skywind-group/sw-wallet-adapter-core`:

- **`PlayMode.REAL`** - Real money gameplay (default)
- **`PlayMode.PLAY_MONEY`** - Virtual/social currency gameplay
- **`PlayMode.BNS`** - Bonus coins gameplay
- **`PlayMode.FUN`** - Fun/demo mode

## Configuration

### Merchant Configuration

Play money support is determined by the merchant's entity configuration:

```typescript
interface MerchantParams {
    playMoneyCurrencies?: string[];  // List of currencies treated as play money
    // ... other params
}
```

**Note:** The `supportPlayMoney` flag is no longer needed as every merchant has an entity with an allowed currencies list that determines play money support.

### Validation Requirements

For `PlayMode.PLAY_MONEY` to be valid:
1. Brand must be a merchant (`brand.isMerchant = true`)
2. Currency must be in the entity's allowed currencies list (replaces `supportPlayMoney` flag)
3. Brand and game cannot be in test status (unless token is marked as test)

## Usage Patterns

### 1. Play Mode Detection and Conversion

The system automatically converts play modes based on currency:

```typescript
// playModeConverter.ts
function playModeConverter(playMode: PlayMode | undefined,
                          currency: string | undefined,
                          merchantInfo: MerchantInfo): PlayMode {
    // Check if currency is in entity's allowed currencies list
    if (currency && merchantInfo.entity?.allowedCurrencies) {
        const { params: { playMoneyCurrencies } } = merchantInfo;
        if (Array.isArray(playMoneyCurrencies) && playMoneyCurrencies.length) {
            if (playMoneyCurrencies.includes(currency.toUpperCase())) {
                return PlayMode.PLAY_MONEY;
            }
        }
    }
    return playMode;
}
```

### 2. Game Token Data

Play money mode is embedded in game tokens:

```typescript
interface GameTokenData {
    playmode: GameMode;  // "play_money" for play money mode
    currency: string;    // The virtual currency code
    // ... other fields
}
```

## Business Logic Differences

### 1. Currency Exchange Behavior

Play money mode has special currency exchange logic:

```typescript
// In PlayMode.exchange()
if (gameData.gameTokenData.playmode === "play_money") {
    if (Currencies.get(targetCurrency).isSocial) {
        return getCurrencyExchange()
            .exchange(amount, baseCurrency || playerCurrency, targetCurrency);
    } else {
        return amount;  // No exchange for non-social currencies
    }
}
```

**Key Points:**
- Only exchanges between social currencies
- Non-social currency exchanges return the original amount
- Prevents conversion to real money currencies

### 2. Jackpot Support

Play money mode **does not support jackpots**:

```typescript
// In jackpot.ts
export async function initJackpot(gameData: GameData): Promise<[JackpotContext, JackpotShortInfo[]]> {
    if (!PlayMode.supportJP(gameData.gameTokenData.playmode)) {
        return [undefined, undefined];  // No jackpot for play money
    }
    // ... jackpot initialization
}
```

### 3. RTP (Return to Player) Restrictions

Play money mode has limited RTP configuration support:

```typescript
// In gameRTPHistory.ts
public playModeSupported(playmode: PlayMode, entitySettings: EntitySettings) {
    return playmode === PlayMode.REAL || (playmode === PlayMode.FUN && entitySettings.rtpDeductionFunModeEnabled);
    // PlayMode.PLAY_MONEY is NOT supported for RTP deduction
}
```

**RTP Limitations:**
- No RTP deduction capabilities
- Cannot configure operator margins
- Uses base game RTP only
- No dynamic RTP adjustments
- Feature RTP remains unchanged

### 4. Slot Game Mechanics

Play money mode supports most slot functionality but with virtual currency:

**Slot Features Available:**
```typescript
// Slot game limits for play money mode
interface SlotGameLimits {
    maxTotalStake: number;     // Virtual currency limits
    winMax: number;            // Virtual currency win limits
    coins?: number[];          // Virtual coin values
    defaultCoin?: number;      // Default virtual coin
    stakeAll: number[];        // Available virtual stakes
}
```

**Supported Slot Features:**
- Full reel mechanics with all symbols
- Wild symbols with substitution and multipliers
- Scatter symbols triggering bonus features
- Free spins with complete mechanics
- Bonus rounds and feature games
- Payline calculations and win evaluations
- Multi-stage game support

**Play Money Specific Behavior:**
- All calculations use virtual currency
- No real money risk or reward
- Same game mechanics as real mode
- Identical RNG and fairness
- Complete game state persistence

### 5. Transfer Functionality

Transfer capabilities depend on merchant configuration:

```typescript
// Transfer enabled only if merchant supports it
settings.transferEnabled = isTransferEnabled(entityGame, merchant, playmode);
```

## Integration Points

### 1. Merchant Adapter Integration

Play money mode integrates with various merchant adapter types:

**Supported Adapter Types:**
- **IPM (Integrated Payment Module)**: Full play money support
- **Seamless**: Virtual currency transactions
- **POP (Player Operator Platform)**: Play money wallet operations
- **GVC**: Virtual currency integration
- **MRCH**: Merchant-specific play money handling

**Adapter Pattern:**
```typescript
// Base adapter implementation supports play money
export class MerchantAdapterImpl<GI extends MerchantGameInitRequest,
    SGT extends MerchantStartGameTokenData,
    AUTHT extends MerchantGameTokenData> implements MerchantAdapter<GI, SGT, AUTHT> {

    public commitPayment(merchant: MerchantInfo, authToken: AUTHT, request: PaymentRequest): Promise<Balance> {
        // Play money payments processed through virtual currency logic
    }
}
```

### 2. Play Service Factory

The system uses a specialized play service for play money:

```typescript
// In defaultPlayServiceFactory.ts
switch (playmode) {
    case PlayMode.PLAY_MONEY:
        playService = new PlayMoneyMerchantPlayService(operatorDetails, playService);
        break;
    // ... other modes
}
```

### 3. Seamless Integration

Play money mode works with seamless wallet integrations:

```typescript
// Seamless game initialization with play money
export interface SeamlessGameInitRequest extends MerchantGameInitRequest {
    currency?: string;           // Virtual currency code
    playmode?: PlayMode;         // PLAY_MONEY mode
    ipmSessionId: string;        // Session for virtual transactions
}

// URL generation includes play mode context
public async createGameUrl(merchant: MerchantInfo,
                          gameCode: string,
                          providerCode: string,
                          providerGameCode: string,
                          initRequest: SeamlessGameInitRequest): Promise<SeamlessGameUrlInfo> {
    const tokenData = await this.getStartGameTokenData(merchant, gameCode, providerCode, providerGameCode, initRequest);
    return {
        tokenData,
        urlParams: this.getUrlParams(initRequest, tokenData.playmode)  // Play mode affects URL params
    };
}
```

### 4. Currency Conversion in Integrations

Play money mode has special currency handling:

```typescript
// Currency conversion for play money
export function playModeConverter(playMode: PlayMode | undefined,
                                 currency: string | undefined,
                                 merchantInfo: MerchantInfo): PlayMode {
    // Check if currency is in entity's allowed currencies list
    if (currency && merchantInfo.entity?.allowedCurrencies) {
        const { params: { playMoneyCurrencies } } = merchantInfo;
        if (Array.isArray(playMoneyCurrencies) && playMoneyCurrencies.length) {
            if (playMoneyCurrencies.includes(currency.toUpperCase())) {
                return PlayMode.PLAY_MONEY;  // Auto-convert to play money mode
            }
        }
    }
    return playMode;
}
```

**Currency Features:**
- Automatic play mode detection based on currency
- Social currency exchange support
- Virtual currency conversion utilities
- Integration-specific currency mapping

### 5. PlayMoneyMerchantPlayService

This service handles play money-specific operations:
- Wraps the standard merchant play service
- Handles payment operations for virtual currencies
- Manages balance operations for play money accounts
- Provides specialized error handling for unsupported operations

### 6. Game URL Generation

Play money mode affects game URL generation:
- Special handling in `BaseGameUrlStrategy`
- Different URL parameters for play money sessions
- Maintains play mode context throughout the game session

## Data Flow

### 1. Game Initialization
1. Start game token contains play mode information
2. System validates play money support
3. Specialized play service is instantiated
4. Game context is created with play money mode

### 2. Payment Processing
1. All payments go through `PlayMoneyMerchantPlayService`
2. Virtual currency transactions are processed via merchant adapters
3. Balance updates use play money wallet logic
4. No real money is involved in any transaction
5. Adapter-specific virtual currency handling

**Integration-Specific Payment Flow:**
```typescript
// IPM adapter payment processing for play money
public async commitPayment(merchant: MerchantInfo, authToken: IPMAuthTokenData, request: PaymentRequest): Promise<Balance> {
    // Virtual currency payment processing
    const virtualBalance = await this.processVirtualCurrencyPayment(merchant, authToken, request);
    return virtualBalance;
}

// Seamless adapter payment processing
private async transferIn(merchant: MerchantInfo,
                        gameToken: SeamlessAuthTokenData,
                        request: MerchantTransferRequest): Promise<Balance> {
    // Virtual currency transfer logic
    const transferInData = this.getPaymentData(EVENT_TYPE.TRANSFER_IN, request.amount, merchant, gameToken, request);
    return await this.processVirtualTransfer(transferInData);
}
```

### 3. Session Management
1. Game sessions maintain play money context across adapters
2. All game operations respect play money limitations
3. Recovery and finalization handle play money appropriately
4. Adapter-specific session handling for virtual currencies

## Related Components

### 1. Currency System
- Integration with `@skywind-group/sw-currency-exchange`
- Support for social currencies
- Virtual currency exchange rates

### 2. Wallet System
- Specialized wallet operations for play money
- Balance management for virtual currencies
- Transaction logging and audit trails

### 3. Adapter Decorators and Regulations

Play money mode works with regulatory decorators:

```typescript
// Italian regulation decorator example
export class SeamlessItalianRegulation
    extends MerchantAdapterDecorator<SeamlessGameInitRequest, SeamlessStartGameTokenData, SeamlessAuthTokenData> {
    // Handles play money mode with regulatory compliance
}
```

**Regulatory Features:**
- AAMS compliance for Italian jurisdiction
- British regulation support
- Play money mode regulatory validation
- Jurisdiction-specific play money rules

### 4. Merchant Adapter Lookup

The system supports multiple adapter types for play money:

```typescript
// Adapter lookup table supports play money across all types
export const internalAdapters: LookupTable = {
    ipm: (merchantInfo: MerchantInfo) => IPMFactory.create(properties.parse, merchantInfo),
    mrch: (merchantInfo: MerchantInfo) => IPMFactory.create(JSON.parse, merchantInfo, true),
    gvc: (merchantInfo: MerchantInfo) => new GVCAdapter(merchantInfo.params),
    pop: (merchantInfo: MerchantInfo) => POPFactory.create(merchantInfo)
    // All adapters support play money mode
};
```

### 5. Game Context
- Play money mode affects game state management
- Special handling in context recovery
- Mode-specific game settings and limits

## Error Handling

Common play money-related errors:
- `ValidationError`: "Play money is not supported operation"
- `UnsupportedPlayMethodError`: For operations not available in play money mode
- `PlayerTestModeError`: When test restrictions apply

## Slot Engine Specific Features

### 1. Game Mechanics in Play Money Mode
Play money mode maintains full slot functionality:

**Symbol Processing:**
- Wild symbol substitution works identically
- Scatter symbol detection and bonus triggering
- Symbol combination evaluation using virtual currency
- Payline win calculations with virtual payouts

**Bonus Features:**
- Free spins with virtual currency wins
- Bonus rounds and mini-games function normally
- Progressive features work without real jackpots
- Multi-stage games maintain full functionality

**Virtual Currency Integration:**
- All bet amounts use virtual currency
- Win calculations apply to virtual balance
- Bonus multipliers affect virtual winnings
- Game state persistence works identically

### 2. Slot Configuration Differences
```typescript
// Play money slot limits
const playMoneyLimits = {
    coins: [1],                    // Virtual coin values
    defaultCoin: 1,               // Default virtual coin
    maxTotalStake: 500,           // Virtual currency limit
    stakeAll: [0.1, 0.5, 1, 2, 3, 5], // Virtual stake options
    stakeDef: 1,                  // Default virtual stake
    stakeMax: 10,                 // Max virtual stake
    stakeMin: 0.1,                // Min virtual stake
    winMax: 3000000,              // Max virtual win
    currencyMultiplier: 100       // Virtual currency multiplier
};
```

### 3. RTP Behavior
- Uses base game RTP without deductions
- No operator margin configuration
- Feature RTP remains at design values
- Theoretical RTP matches actual RTP more closely

## Testing

The system includes comprehensive test suites for play money functionality:
- `gameContext.play_money.spec.ts` - Game context behavior
- `gameController.play_money.spec.ts` - Game controller operations
- `gameRecovery.finalizePlayMoney.spec.ts` - Recovery scenarios
- `playMode.spec.ts` - Core play mode logic

### Slot-Specific Testing
- Test virtual currency calculations
- Validate bonus feature triggers with virtual wins
- Test free spin mechanics with virtual currency
- Verify game state persistence in play money mode
- Test slot limits and stake configurations

## Best Practices

1. **Always validate play money support** before enabling the mode
2. **Use appropriate currencies** configured in `playMoneyCurrencies`
3. **Handle unsupported operations gracefully** (transfers, jackpots)
4. **Maintain clear separation** between real and virtual currency flows
5. **Test thoroughly** with play money-specific test scenarios

## Implementation Examples

### 1. Merchant Configuration Example

```json
{
  "merchantParams": {
    "playMoneyCurrencies": ["FUN", "DEMO", "SOCIAL"],
    "supportTransfer": true,
    "walletPerGame": false
  }
}
```

### 2. Game Token Example

```typescript
const gameTokenData: GameTokenData = {
    playerCode: "PLAYER123",
    gameCode: "SLOT001",
    brandId: 1,
    currency: "FUN",
    playmode: "play_money",
    test: false
};
```

### 3. Slot Game Configuration for Play Money

```typescript
// Slot game configuration works identically in play money mode
const slotConfig = {
    scenes: [
        new SlotScene({
            name: 'main',
            symbols: {
                Wild: {id: 12, wild: true},
                Scatter: {id: 13, payId: 6, scatter: true, multiplier: [0, 0, 0, 0, 0]}
            },
            reels: {
                rows: [4, 4, 4, 4, 4],
                strips: [/* same reel strips as real mode */]
            },
            rules: [
                new WildRule({
                    ids: [12],
                    symbols: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11],
                    multiplier: [2, 2, 2, 2, 2]  // Same multipliers
                }),
                new ScatterRule({
                    ids: [13],
                    scene: 'freeSpins',
                    rewards: [
                        {freeSpins: 12, multiplier: 3}  // Same bonus mechanics
                    ],
                    betMultiplier: [0, 2, 4, 30, 400]  // Virtual currency multipliers
                })
            ]
        })
    ],
    paytable: [
        [0, 0, 6, 20, 200],    // Same paytable values
        [0, 0, 8, 24, 250],    // Applied to virtual currency
        // ... identical to real mode
    ]
};
```

### 4. Payment Operation Example

```typescript
// Play money payment request
const paymentRequest: PaymentRequest = {
    transactionId: "TXN_123",
    bet: 100,  // 100 FUN coins
    win: 250,  // 250 FUN coins (from slot win calculation)
    roundId: "ROUND_456"
};

// Processed through PlayMoneyMerchantPlayService
const balance = await playService.commitGamePayment(gameTokenData, paymentRequest);
```

### 5. Game State Management

```typescript
// Game context for play money mode includes full slot state
const gameContext = {
    currentScene: "main",
    nextScene: "freeSpins",
    roundEnded: false,
    specialState: {
        freeSpinsRemaining: 10,  // Free spins work normally
        multiplier: 3,           // Multipliers apply to virtual wins
        bonusTriggered: true
    }
};
```

## Limitations and Restrictions

### 1. Jackpot Restrictions
- **No jackpot support** in play money mode
- Jackpot initialization returns `undefined`
- Games with mandatory jackpots cannot use play money mode

### 2. Transfer Limitations
- Transfer functionality depends on merchant configuration
- Some transfer operations may be restricted
- External transfers typically not supported

### 3. Currency Exchange Restrictions
- Only social currencies can be exchanged
- No conversion to real money currencies
- Exchange rates may be simplified or fixed

### 4. Slot-Specific Restrictions
- **Jackpots**: No progressive or instant jackpots available
- **RTP Deduction**: Cannot configure operator margins
- **Real Money Features**: No conversion to real currency
- **Bonus Buy**: Virtual currency bonus purchases only

### 5. Promotional Restrictions
- Bonus coins (BNS) mode is separate from play money
- Free bets may have limited support
- Promotional wallets may behave differently

## Monitoring and Analytics

### 1. Transaction Tracking
- All play money transactions are logged
- Separate reporting from real money transactions
- Virtual currency balance tracking

### 2. Performance Metrics
- Play money session duration
- Virtual currency consumption patterns
- Conversion rates from play money to real money

### 3. Business Intelligence
- Player engagement with virtual currencies
- Effectiveness of promotional campaigns
- Demo-to-real conversion analytics

## Security Considerations

### 1. Virtual Currency Protection
- Prevent conversion to real money
- Validate all play money operations
- Audit virtual currency flows

### 2. Fraud Prevention
- Monitor unusual play money patterns
- Prevent exploitation of virtual currency systems
- Maintain separation from real money systems

### 3. Compliance
- Ensure virtual currencies comply with regulations
- Maintain proper documentation
- Separate reporting for virtual vs real transactions

## Troubleshooting

### Common Issues

1. **"Play money is not supported operation"**
   - Verify brand is merchant type (`brand.isMerchant = true`)
   - Check that the currency is in the entity's allowed currencies list
   - Note: `supportPlayMoney` flag is no longer used

2. **Currency not recognized as play money**
   - Ensure currency is in `playMoneyCurrencies` array
   - Check currency code case sensitivity
   - Verify currency is in the entity's allowed currencies list

3. **Jackpot errors in play money mode**
   - Jackpots are not supported in play money mode
   - Use games without mandatory jackpots
   - Configure games to disable jackpot requirements

4. **Transfer operations failing**
   - Check merchant transfer support configuration
   - Verify transfer is enabled for the game

5. **RTP configuration issues**
   - RTP deduction not supported in play money mode
   - Use base game RTP settings only
   - Remove RTP configurator settings for play money

6. **Slot game limit errors**
   - Ensure virtual currency limits are properly configured
   - Check currency multiplier settings
   - Validate stake configurations for virtual currency

7. **Integration adapter issues**
   - Verify adapter supports play money mode
   - Check merchant adapter configuration
   - Validate currency conversion settings
   - Ensure seamless integration parameters are correct

8. **Currency conversion problems**
   - Check `playMoneyCurrencies` configuration
   - Verify social currency settings
   - Validate currency exchange rates for virtual currencies
   - Ensure adapter-specific currency mapping is correct

## Code References

### Key Files and Locations

#### Core PlayMode Logic
- **`src/skywind/services/playMode.ts`** - Main PlayMode class with exchange logic (sw-integration-seamless)
- **`packages/api/src/skywind/utils/playModeConverter.ts`** - Play mode detection and conversion (sw-management-api)
- **`packages/api/src/skywind/utils/validatePlaymode.ts`** - Play mode validation (sw-management-api)

#### Integration Adapters
- **`packages/adapters/src/skywind/ipmadapter.ts`** - IPM adapter with play money support
- **`packages/adapters/src/skywind/baseAdapter.ts`** - Base adapter implementation
- **`packages/adapters/src/skywind/model.ts`** - Merchant adapter interfaces
- **`packages/adapters/src/skywind/lookup.ts`** - Adapter lookup and factory
- **`src/skywind/wallet/services/game.ts`** - Seamless game service
- **`src/skywind/wallet/services/payment.ts`** - Seamless payment service
- **`src/skywind/wallet/services/balance.ts`** - Seamless balance service

#### Play Services
- **`packages/playservice/src/skywind/playMoneyMerchantPlayService.ts`** - Main play money service
- **`packages/gameprovider-core/src/skywind/playservicefactory/defaultPlayServiceFactory.ts`** - Service factory
- **`packages/playservice/src/skywind/playServiceImpl.ts`** - Base play service implementation
- **`packages/playservice/src/skywind/merchantTransferablePlayService.ts`** - Transferable play service

#### Configuration and Entities
- **`packages/api/src/skywind/entities/merchant.ts`** - Merchant configuration interface
- **`packages/api/src/skywind/services/playService.ts`** - Game settings and validation
- **`src/skywind/services/tokens.ts`** - Token data structures
- **`src/skywind/entities/seamless.ts`** - Seamless integration entities

#### Currency and Exchange
- **`src/skywind/utils/currencyConverter.ts`** - Currency conversion utilities
- **`src/skywind/services/currencyexchange.ts`** - Currency exchange service

#### Regulatory Integration
- **`src/skywind/regulations/italian.ts`** - Italian regulation decorator
- **`packages/adapters/src/skywind/IPMAdapterDecoratorForBritishRegulation.ts`** - British regulation
- **`packages/adapters/src/skywind/ipmAdapterDecoratorForItalianRegulation.ts`** - Italian regulation

#### Game Integration
- **`src/skywind/services/jpn/jackpot.ts`** - Jackpot support logic
- **`packages/api/src/skywind/services/gameUrl/baseGameUrlStrategy.ts`** - URL generation
- **`src/skywind/services/contextmanager/contextManagerImpl.ts`** - Game context management

### API Endpoints

#### Merchant Configuration
- **GET** `/api/v2/merchants/{id}` - Retrieve merchant configuration
- **PUT** `/api/v2/merchants/{id}` - Update merchant settings (entity's allowed currencies determine play money support)

#### Game Operations
- **POST** `/api/v2/games/start` - Start game with play mode specification
- **POST** `/api/v2/games/payment` - Process play money payments
- **GET** `/api/v2/games/balance` - Get play money balance

#### Validation
- **POST** `/api/v2/games/validate` - Validate play mode support

### Environment Variables

```bash
# Play money related configurations
EGP_CONFIGURATIONS={"PP":{"url":"http://localhost:3250/","promoLocation":"EGP"}}
WALLET_CONDUCTOR_TYPE=http  # Affects promo check behavior
```

### Database Schema

#### Merchant Table
```sql
-- merchant.params JSONB field contains:
{
  "playMoneyCurrencies": string[],
  "supportTransfer": boolean,
  "walletPerGame": boolean
}
-- Note: supportPlayMoney flag removed - play money support determined by entity's allowed currencies
```

#### Game Token Storage
- Play mode is embedded in JWT tokens
- No separate database storage for play mode state
- Context stored in game session management

## Future Considerations

For promotional currency implementations:
- Consider how play money integrates with bonus systems
- Plan for promotional currency exchange rates
- Design appropriate limits and restrictions
- Ensure proper financial reporting separation
- Implement conversion mechanisms between virtual and bonus currencies
- Design promotional campaign tracking and analytics
- Plan for regulatory compliance with virtual currency promotions

### Recommended Architecture for New Play Modes

1. **Extend PlayMode enum** with new promotional modes
2. **Create specialized MerchantPlayService** similar to PlayMoneyMerchantPlayService
3. **Implement currency conversion** logic for promotional currencies
4. **Add campaign tracking** for promotional activities
5. **Design Finance integration** for promotional cost tracking
6. **Implement time-limited** promotional currency mechanics
