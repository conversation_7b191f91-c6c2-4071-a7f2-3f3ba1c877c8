# PlayMode.FUN_BONUS Implementation Plan

## Overview

This document outlines the comprehensive implementation plan for adding `PlayMode.FUN_BONUS` to the Skywind gaming platform. This new play mode restricts gameplay to **currencies with funBonus property only**, ensuring no real money transactions while maintaining the same game mechanics. It follows the same architectural patterns as the existing special currency feature designed for FunBonus activities.

## Architecture Overview

The new PlayMode.FUN_BONUS will:
- Follow the same patterns as existing play modes (REAL, PLAY_MONEY, BNS, FUN)
- **Restrict gameplay to currencies with `funBonus: true` property only**
- Use currency-level configuration via new `funBonus` property in currency definitions
- Use the existing play service factory pattern for service instantiation
- Leverage the current currency validation and exchange infrastructure for funBonus currencies
- Maintain full backward compatibility with PlayMode.REAL

## Key Implementation Points

1. **Currency-Level FunBonus Property**: Only currencies with `funBonus: true` property are allowed in FUN_BONUS mode
2. **Automatic Mode Detection**: The `playModeConverter` will automatically switch to FUN_BONUS mode for currencies with funBonus property
3. **Service Layer Integration**: Uses the same play service architecture with funBonus currency validation
4. **Currency Validation**: Implements robust validation ensuring only funBonus currencies are accepted
5. **Currency Configuration Changes**: Add `funBonus` property to currency definitions in sw-currency-exchange
6. **Real Money Prevention**: Strict validation prevents any real money currency usage in FUN_BONUS mode

## Multi-Repository Implementation

Changes needed across 6 repositories:
- `sw-currency-exchange` (add funBonus property to currency definitions)
- `sw-game-core` (update PlayMode enum and GameMode type)
- `sw-wallet-adapter-core` (core enum and types)
- `sw-management-api` (validation, services, API endpoints)
- `sw-integration-seamless` (converter utilities)
- `sw-slot-engine` (slot game logic, currency exchange, context management)

---

## Phase 1: Core Foundation (Critical Priority)

### Task 1: Currency FunBonus Property Addition
- **Repository:** `sw-currency-exchange`
- **Priority:** Critical
- **Story Points:** 3
- **Dependencies:** None
- **Files to Modify:**
  - `src/skywind/currencies.ts` - Add funBonus property to CurrencyInfo interface
  - `src/skywind/resources/currencies.json` - Add funBonus property to selected currencies
- **Implementation:**
  ```typescript
  interface CurrencyInfo {
      readonly name: string;
      readonly iso: Iso;
      readonly type?: "virtual" | "social";
      readonly toEURMultiplier?: number;
      readonly copyLimitsFrom?: string;
      readonly provider?: string;
      readonly clientMinorUnits?: number;
      readonly clientMoneyFormat?: ClientMoneyFormat;
      readonly disableGGR?: boolean;
      readonly funBonus?: boolean; // NEW PROPERTY
  }
  
  export class Currency {
      public readonly funBonus: boolean; // NEW PROPERTY
      
      constructor(
          // ... existing parameters
          funBonus?: boolean
      ) {
          // ... existing assignments
          this.funBonus = funBonus || false;
      }
  }
  ```
- **Acceptance Criteria:**
  - funBonus property added to CurrencyInfo interface
  - Currency class updated with funBonus property
  - Selected currencies marked with funBonus: true in currencies.json
  - Backward compatibility maintained

### Task 2: Core PlayMode and GameMode Updates
- **Repository:** `sw-game-core`
- **Priority:** Critical
- **Story Points:** 3
- **Dependencies:** Task 1
- **Files to Modify:**
  - `src/skywind/definitions/startGame.ts` - Add `FUN_BONUS = "fun_bonus"`
  - `src/definition.ts` - Add "fun_bonus" to GameMode type
- **Implementation:**
  ```typescript
  // In startGame.ts
  export enum PlayMode {
      REAL = "real",
      FUN = "fun",
      BNS = "bns",
      PLAY_MONEY = "play_money",
      FUN_BONUS = "fun_bonus",
  }
  
  // In definition.ts
  export type GameMode = "real" | "fun" | "bns" | "play_money" | "fun_bonus";
  ```
- **Acceptance Criteria:**
  - FUN_BONUS enum value added to PlayMode enum
  - "fun_bonus" added to GameMode type
  - Properly exported from package
  - No breaking changes to existing enum usage

### Task 3: Wallet Adapter Core Updates
- **Repository:** `sw-wallet-adapter-core`
- **Priority:** Critical
- **Story Points:** 2
- **Dependencies:** Task 2
- **Files to Modify:**
  - `src/skywind/definitions/startGame.ts` - Add `FUN_BONUS = "fun_bonus"`
- **Implementation:**
  ```typescript
  // In startGame.ts
  export enum PlayMode {
      REAL = "real",
      FUN = "fun",
      BNS = "bns",
      PLAY_MONEY = "play_money",
      FUN_BONUS = "fun_bonus",
  }
  ```
- **Acceptance Criteria:**
  - FUN_BONUS enum value added to PlayMode enum
  - Properly exported from package
  - No breaking changes to existing enum usage

---

## Phase 2: Core Logic Implementation (High Priority)

### Task 4: Play Mode Converter Enhancement
- **Repository:** `sw-integration-seamless`
- **Priority:** High
- **Story Points:** 3
- **Dependencies:** Tasks 1-3
- **Files to Modify:**
  - `src/skywind/utils/playModeConverter.ts`
- **Logic:**
  ```typescript
  import { Currencies } from "@skywind-group/sw-currency-exchange";
  
  // Add funBonus property check
  if (currency) {
      const currencyObj = Currencies.value(currency.toUpperCase());
      if (currencyObj?.funBonus) {
          return PlayMode.FUN_BONUS;
      }
  }
  ```
- **Acceptance Criteria:**
  - Auto-conversion to FUN_BONUS when currency has funBonus property
  - Only currencies with funBonus: true trigger FUN_BONUS mode
  - Currency-driven mode detection
  - Maintains existing PLAY_MONEY logic
  - Proper error handling for invalid configurations

### Task 5: Currency Validation Logic
- **Repository:** `sw-management-api`
- **Priority:** High
- **Story Points:** 4
- **Dependencies:** Tasks 1-4
- **Files to Modify:**
  - `packages/api/src/skywind/utils/validatePlaymode.ts`
  - Create new `validateFunBonus.ts` utility
- **Implementation:**
  ```typescript
  import { Currencies } from "@skywind-group/sw-currency-exchange";
  
  export function validateFunBonus(
      brand: BrandEntity, 
      currency: string, 
      merchant?: Merchant
  ): boolean {
      // Validate that currency has funBonus property
      const currencyObj = Currencies.value(currency.toUpperCase());
      if (!currencyObj) {
          throw new ValidationError(`Currency ${currency} not found`);
      }
      
      if (!currencyObj.funBonus) {
          throw new ValidationError(`Currency ${currency} is not a fun bonus currency. Only currencies with funBonus property are allowed in fun bonus mode`);
      }
      
      return true;
  }
  ```
- **Acceptance Criteria:**
  - Validates that currency has funBonus property
  - Rejects currencies without funBonus property
  - No merchant configuration validation needed
  - Provides clear error messages explaining funBonus currency requirement
  - Follows existing validation patterns

### Task 6: PlayMode Service Logic Updates
- **Repository:** `sw-integration-seamless`
- **Priority:** High
- **Story Points:** 5
- **Dependencies:** Tasks 1-5
- **Files to Modify:**
  - `src/skywind/services/playMode.ts`
- **Implementation:**
  - Add FUN_BONUS case to `supportJP()` method
  - Update `exchange()` method to handle FUN_BONUS like REAL mode (funBonus currencies use existing exchange logic)
  - Add funBonus currency validation in `getCurrency()` method
- **Acceptance Criteria:**
  - FUN_BONUS behaves like REAL mode for most operations
  - Currency exchange works correctly for funBonus currencies
  - Jackpot support enabled (like REAL mode)
  - FunBonus currency exchange uses existing sw-currency-exchange logic

### Task 6a: Slot Engine PlayMode Service Updates
- **Repository:** `sw-slot-engine`
- **Priority:** High
- **Story Points:** 4
- **Dependencies:** Tasks 1-5
- **Files to Modify:**
  - `src/skywind/services/playMode.ts`
- **Implementation:**
  ```typescript
  export class PlayMode {
      public static supportJP(gameMode: GameMode) {
          return gameMode === "real" || gameMode === "fun_bonus" || !gameMode;
      }

      public static exchange(amount: number, baseCurrency: string, targetCurrency: string, gameData: GameData): number {
          // Add FUN_BONUS case to handle like REAL mode
          if (gameData.gameTokenData.playmode === "fun_bonus") {
              return getCurrencyExchange().exchange(amount, baseCurrency || playerCurrency, targetCurrency);
          }
          // ... existing logic
      }
  }
  ```
- **Acceptance Criteria:**
  - FUN_BONUS supports jackpots like REAL mode
  - Currency exchange works correctly in slot games
  - No breaking changes to existing slot game logic

---

## Phase 3: Service Integration (High Priority)

### Task 7: Play Service Factory Integration
- **Repository:** `sw-management-api`
- **Priority:** High
- **Story Points:** 4
- **Dependencies:** Tasks 1-6
- **Files to Modify:**
  - `packages/gameprovider-core/src/skywind/playservicefactory/defaultPlayServiceFactory.ts`
- **Implementation:**
  - Add FUN_BONUS case to switch statement
  - Use standard MerchantPlayService (like REAL mode)
  - No special wrapper needed
- **Acceptance Criteria:**
  - FUN_BONUS uses same service as REAL mode
  - No breaking changes to existing factory logic
  - Proper service instantiation

### Task 8: Validation Middleware Updates
- **Repository:** `sw-management-api`
- **Priority:** High
- **Story Points:** 2
- **Dependencies:** Task 5
- **Files to Modify:**
  - `packages/api/src/skywind/utils/validatePlaymode.ts`
- **Implementation:**
  ```typescript
  export function validatePlaymode(brand: BrandEntity, playmode: PlayMode = PlayMode.REAL, merchant?: Merchant): boolean {
      if (playmode === PlayMode.PLAY_MONEY) {
          if (!(brand.isMerchant && merchant?.params?.supportPlayMoney)) {
              throw new ValidationError("Play money is not supported operation");
          }
      }
      
      // FUN_BONUS validation is handled at currency level - no merchant config needed
      // Currency validation occurs in validateFunBonus function
      
      return true;
  }
  ```
- **Acceptance Criteria:**
  - FUN_BONUS mode requires no merchant-level validation
  - Currency-level validation handles funBonus property check
  - Maintains backward compatibility
  - Clear separation of concerns

### Task 9: Game URL Strategy Updates
- **Repository:** `sw-management-api`
- **Priority:** High
- **Story Points:** 4
- **Dependencies:** Tasks 1-8
- **Files to Modify:**
  - `packages/api/src/skywind/services/gameUrl/baseGameUrlStrategy.ts`
  - `packages/api/src/skywind/services/gameUrl/merchantGameUrlStrategy.ts`
- **Implementation:**
  - Handle FUN_BONUS in game token generation
  - Add currency validation during URL creation
  - Ensure proper mode propagation
- **Acceptance Criteria:**
  - Game tokens include FUN_BONUS mode
  - Currency validation occurs during game initialization
  - URL generation works correctly

### Task 9a: Slot Engine Game Flow Context Updates
- **Repository:** `sw-slot-engine`
- **Priority:** High
- **Story Points:** 3
- **Dependencies:** Tasks 6a, 9
- **Files to Modify:**
  - `src/skywind/services/gameFlowFactory.ts`
  - `src/skywind/services/gamecontroller.ts`
- **Implementation:**
  - Update `getGameFlowContextManager()` to handle FUN_BONUS mode
  - Ensure proper context management for fun bonus games
  - Add currency validation in game flow initialization
- **Acceptance Criteria:**
  - FUN_BONUS mode uses appropriate context manager
  - Game flow initialization validates currency restrictions
  - No impact on existing game flow logic

### Task 9b: Slot Engine Currency Validation
- **Repository:** `sw-slot-engine`
- **Priority:** High
- **Story Points:** 4
- **Dependencies:** Task 6a
- **Files to Modify:**
  - `src/skywind/wallet/services/game.ts`
  - `src/skywind/utils/playModeConverter.ts` (already updated)
- **Implementation:**
  - Add currency validation in seamless token mapping
  - Ensure FUN_BONUS mode validates against currency funBonus property
  - Update currency conversion logic for funBonus currencies
- **Acceptance Criteria:**
  - Currency validation occurs during slot game initialization
  - Only currencies with funBonus property are accepted
  - Invalid currencies are rejected with clear error messages
  - Seamless integration maintains funBonus currency restrictions

---

## Phase 4: API and Configuration (Medium Priority)

### Task 10: API Endpoint Updates
- **Repository:** `sw-management-api`
- **Priority:** Medium
- **Story Points:** 3
- **Dependencies:** Tasks 1-9
- **Files to Modify:**
  - Game initialization endpoints
  - API validation schemas
- **Implementation:**
  - Support FUN_BONUS in game start APIs
  - Update OpenAPI/Swagger documentation
  - Add currency funBonus property validation
- **Acceptance Criteria:**
  - APIs accept FUN_BONUS mode
  - Currency validation checks funBonus property
  - Proper API validation

### Task 11: Configuration Management
- **Repository:** `sw-management-api` and `sw-currency-exchange`
- **Priority:** Medium
- **Story Points:** 4
- **Dependencies:** Task 1
- **Files to Modify:**
  - Currency service layer
  - Configuration validation utilities
- **Implementation:**
  - Add utilities for managing funBonus currency configurations
  - Add helper methods to list available funBonus currencies
  - Support for updating currency funBonus properties
  - Validate funBonus currency availability
- **Acceptance Criteria:**
  - Configuration utilities for funBonus currency management
  - Helper methods to display available funBonus currencies
  - Support for currency configuration updates
  - Validation ensures funBonus currencies are properly configured

### Task 12: Error Handling and Messages
- **Repository:** `sw-management-api`
- **Priority:** Medium
- **Story Points:** 3
- **Dependencies:** Task 5
- **Files to Modify:**
  - `packages/api/src/skywind/errors/index.ts`
- **Implementation:**
  ```typescript
  export class CurrencyNotFunBonusEnabledError extends ValidationError {
      constructor(currency: string) {
          super(`Currency ${currency} is not enabled for fun bonus mode. Only currencies with funBonus property can be used in fun bonus mode.`);
      }
  }

  export class FunBonusCurrencyNotFoundError extends ValidationError {
      constructor(currency: string) {
          super(`Currency ${currency} not found or not configured for fun bonus mode.`);
      }
  }
  ```
- **Acceptance Criteria:**
  - Specific error types for funBonus currency scenarios
  - Clear messages explaining funBonus property requirement
  - Emphasis on currency-level configuration
  - Consistent error handling

---

## Phase 5: Documentation and Testing (Medium Priority)

### Task 13: Documentation Updates
- **Repository:** All repositories
- **Priority:** Medium
- **Story Points:** 6
- **Dependencies:** All implementation tasks
- **Files to Create/Modify:**
  - `docs/PlayMode-FUN_BONUS-Documentation.md`
  - Update existing PlayMode documentation
  - API documentation updates
- **Content:**
  - Configuration examples
  - Usage scenarios
  - Integration guides
  - Troubleshooting section
- **Acceptance Criteria:**
  - Comprehensive documentation following existing patterns
  - Configuration examples
  - Integration guides

### Task 14: Unit Tests - Core Logic
- **Repository:** All repositories
- **Priority:** Medium
- **Story Points:** 10
- **Dependencies:** Tasks 1-12, 6a, 9a, 9b
- **Files to Create/Modify:**
  - Test files for all modified components across all 6 repositories
  - Integration test scenarios
- **Test Coverage:**
  - PlayMode converter logic (sw-integration-seamless, sw-slot-engine)
  - Currency validation (sw-management-api, sw-slot-engine)
  - Service factory behavior (sw-management-api)
  - Slot engine game flow and context management
  - Error scenarios across all repositories
- **Acceptance Criteria:**
  - >90% code coverage for new functionality
  - All positive and negative test cases
  - Integration test scenarios including slot games
  - Cross-repository compatibility tests

### Task 15: Integration Tests
- **Repository:** `sw-management-api` and `sw-slot-engine`
- **Priority:** Medium
- **Story Points:** 12
- **Dependencies:** All implementation tasks including slot engine tasks
- **Test Scenarios:**
  - End-to-end game initialization with FUN_BONUS (both regular and slot games)
  - Currency validation flows across all game types
  - Slot-specific currency exchange and jackpot scenarios
  - Cross-repository integration testing
  - Error handling scenarios
- **Acceptance Criteria:**
  - Full end-to-end test coverage including slot games
  - Real-world scenario testing with slot engine integration
  - Performance validation across all repositories
  - Slot game specific testing (currency exchange, jackpots, game flow)

---

## Phase 6: Quality Assurance (Low Priority)

### Task 16: Backward Compatibility Testing
- **Repository:** All repositories
- **Priority:** Low
- **Story Points:** 5
- **Dependencies:** All implementation tasks
- **Test Scenarios:**
  - Existing PlayMode.REAL functionality unchanged
  - Migration scenarios for existing merchants
  - API compatibility verification
- **Acceptance Criteria:**
  - No breaking changes to existing functionality
  - Smooth migration path
  - API backward compatibility

### Task 17: Performance Testing
- **Repository:** All repositories
- **Priority:** Low
- **Story Points:** 4
- **Dependencies:** All implementation tasks
- **Test Areas:**
  - Validation logic performance impact
  - Service factory performance
  - Currency property lookup performance
- **Acceptance Criteria:**
  - No performance degradation
  - Acceptable response times
  - Scalability validation

### Task 18: Security Review
- **Repository:** All repositories
- **Priority:** Low
- **Story Points:** 3
- **Dependencies:** All implementation tasks
- **Review Areas:**
  - Currency restriction bypass possibilities
  - Input validation security
  - Configuration tampering prevention
- **Acceptance Criteria:**
  - No security vulnerabilities
  - Proper input validation
  - Secure configuration handling

---

## Implementation Roadmap

**Sprint 1 (2 weeks):** Tasks 1-3 (Foundation - Currency, Enum & Adapter Core)
**Sprint 2 (3 weeks):** Tasks 4-6, 6a (Core Logic - Management API & Slot Engine)
**Sprint 3 (3 weeks):** Tasks 7-9, 9a, 9b (Service Integration - All Repositories)
**Sprint 4 (2 weeks):** Tasks 10-12 (API & Configuration)
**Sprint 5 (3 weeks):** Tasks 13-15 (Documentation & Testing - Extended for 6 repositories)
**Sprint 6 (2 weeks):** Tasks 16-18 (Quality Assurance)

**Total Estimated Effort:** 15 weeks (72 story points)

## Risk Assessment

### High Risk
- **Multi-repository coordination** - Changes across 6 repositories require careful coordination and version synchronization
- **Currency configuration changes** - Adding funBonus property to currency definitions requires careful rollout
- **Backward compatibility** - Must ensure no breaking changes to existing PlayMode.REAL functionality across all repositories
- **Slot engine integration complexity** - Slot games have complex currency exchange and jackpot logic that must work correctly with FUN_BONUS

### Medium Risk
- **Currency validation complexity** - Need to handle edge cases in funBonus currency logic across multiple game types
- **Performance impact** - Additional validation logic may impact response times, especially in slot games
- **Cross-repository testing** - Integration testing across 6 repositories increases complexity

### Low Risk
- **Simplified merchant configuration** - Removing merchant-level configuration reduces setup complexity
- **Service integration** - Following existing patterns reduces implementation risk
- **Slot engine patterns** - Existing PlayMode handling in slot engine provides clear implementation patterns

## Success Criteria

1. **Functional Requirements Met:**
   - PlayMode.FUN_BONUS successfully restricts gameplay to currencies with funBonus property only
   - No real money transactions possible in FUN_BONUS mode
   - Currency-level configuration provides granular control
   - Full backward compatibility maintained

2. **Technical Requirements Met:**
   - No performance degradation in existing functionality
   - Comprehensive test coverage (>90% for new code)
   - Security review passed with no critical issues
   - FunBonus currency exchange works correctly through existing sw-currency-exchange logic

3. **Operational Requirements Met:**
   - Complete documentation for configuration and usage
   - Successful deployment across all environments
   - Monitoring and alerting in place
   - Clear guidance on available funBonus currencies

## Slot Engine Specific Requirements

### Currency Exchange Integration
- FUN_BONUS mode must integrate seamlessly with slot engine currency exchange logic for funBonus currencies
- Support for multi-currency slot games with funBonus currency validation
- Proper handling of funBonus currency conversion in slot game payouts and jackpots
- Leverage existing currency exchange logic in sw-currency-exchange

### Game Flow Context Management
- Slot engine context managers must properly handle FUN_BONUS mode
- Game flow initialization must validate funBonus currency restrictions before game start
- Multi-stage games must maintain funBonus currency restrictions throughout the flow

### Jackpot Support
- FUN_BONUS mode must support jackpots like REAL mode
- Jackpot contributions and payouts must respect funBonus currency restrictions
- Cross-funBonus-currency jackpot scenarios must be handled correctly

### Seamless Integration
- Slot engine seamless integration must validate funBonus currencies only
- Token mapping must preserve funBonus currency restriction information
- Merchant adapter integration must support funBonus currency validation

## Configuration Examples

### Currency Configuration (sw-currency-exchange)
```json
{
  "USX": {
    "name": "US Dollar Artificial",
    "iso": { "code": "USX", "number": "840", "minorUnits": 2 },
    "funBonus": true
  },
  "EUX": {
    "name": "Euro Artificial",
    "iso": { "code": "EUX", "number": "978", "minorUnits": 2 },
    "funBonus": true
  }
}
```

### Available FunBonus Currencies (Currency-Level Configuration)
Only currencies with `funBonus: true` property are allowed:
- **USX** (US Dollar Artificial - if funBonus: true)
- **EUX** (Euro Artificial - if funBonus: true)
- **GBX** (British Pound Artificial - if funBonus: true)
- **CDX** (Canadian Dollar Artificial - if funBonus: true)
- Any other currency with funBonus property enabled

### Usage Scenario
1. Currency USX is configured with `funBonus: true`
2. Player attempts to start game with USX currency
3. `playModeConverter` automatically switches to `PlayMode.FUN_BONUS`
4. Game initializes successfully with funBonus currency restrictions enforced
5. Player attempts to start game with USD currency (no funBonus property)
6. Validation fails with clear error message about funBonus currency requirement

## Additional Slot Engine Considerations

### Performance Optimization
- Slot games have high-frequency currency operations that must remain performant
- FunBonus currency validation should be optimized for slot game response times
- Leverage existing currency caching for performance
- Simple property check on currency object provides fast validation

### Error Handling
- Slot games require specific error handling for funBonus currency restriction violations
- Game interruption scenarios must be handled gracefully when non-funBonus currencies are attempted
- Player-friendly error messages explaining funBonus currency requirement
- Clear guidance on available funBonus currencies

### Testing Strategy
- Slot-specific test scenarios including multi-stage games with funBonus currencies
- Currency exchange accuracy testing for funBonus currency conversions
- Jackpot functionality testing with funBonus currencies only
- Performance testing for high-frequency slot game operations with funBonus currencies
- Security testing to ensure non-funBonus currencies are completely blocked

## FunBonus Currency Integration Requirements

### Currency Exchange Service Integration
- FUN_BONUS mode must use existing currency exchange logic from sw-currency-exchange
- FunBonus currencies use standard exchange logic (artificial currencies convert through origin, others direct)
- No modifications needed to exchange service - existing logic handles all currency types
- Exchange rates calculated using existing currency exchange mechanisms

### Real Money Prevention
- **Critical Security Requirement**: Only currencies with funBonus property allowed in FUN_BONUS mode
- Validation must occur at multiple layers: converter, service, and API levels
- Error messages must clearly explain funBonus currency requirement
- Audit logging should track any attempts to use non-funBonus currencies in FUN_BONUS mode

### Currency Configuration Management
- **Currency-Level Control**: funBonus property provides granular control over which currencies can be used
- **Flexible Configuration**: Can be applied to artificial currencies, virtual currencies, or even real currencies if needed
- **No Merchant Dependencies**: Eliminates need for merchant-level configuration and management
- **Centralized Control**: All funBonus currency management happens at currency definition level

This implementation plan ensures incremental delivery across 6 repositories while maintaining the architectural patterns established in the existing codebase. The focus on currency-level funBonus property provides flexible control over which currencies can be used in FUN_BONUS mode, while leveraging the existing sw-currency-exchange infrastructure. This approach eliminates merchant configuration complexity and provides centralized currency management for the funBonus feature.
